
const state = {

    currentBaseLayer: 2,
    remainQuota: 0,
    picMode: false,
    tileDate: 0,
    overlay: null,
    signupDialog:false,
    exhaustedDialog:false,
    selectionDialog:false,
    baseLayers: [],
    tileUrl: null,
    cachedBounds: null,
    cachedHighResResults: null,
    cachedImagery: null,
    tileProvider: null,
    tileDateDisplay: null,
    fileOpenTrigger: false

}
const mutations  = {
    ...VuexPathify.make.mutations(state),
    clearData(state) {
        //state.tileProvider = null;
        //state.tileUrl = null;
        state.cachedBounds = null;
        state.cachedHighResResults = null;
        //state.tileDateDisplay = null;
        state.fileOpenTrigger = false;
        state.cachedImagery = null;
        state.signupDialog = false;
        state.exhaustedDialog = false;
        state.selectionDialog = false;
        //state.tileDate = 0;
        state.picMode = false;
        state.overlay = null;
    },
    clearDataAll(state) {
        state.tileProvider = null;
        state.currentBaseLayer = 2;
        state.tileUrl = null;
        state.cachedBounds = null;
        state.cachedHighResResults = null;
        state.tileDateDisplay = null;
        state.fileOpenTrigger = false;
        state.cachedImagery = null;
        state.signupDialog = false;
        state.exhaustedDialog = false;
        state.selectionDialog = false;
        state.tileDate = 0;
        state.picMode = false;
        state.overlay = null;
    }
}

const getters = {
    ...VuexPathify.make.getters(state),
}

export default {
    namespaced: true,
    state,
    getters,
    mutations,
}



