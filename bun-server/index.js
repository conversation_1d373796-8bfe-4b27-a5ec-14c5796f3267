const mapboxApiKey = Bun.env.MAPBOX_API_KEY || 'sk.eyJ1Ijoic2l0ZWZvdG9zIiwiYSI6ImNtNnQ5MmFjcjAwbzgyanNnMzlkOWpucHQifQ.EmtZiIHzxH2g0DH_ZK4UOw';
const maptilerApiKey = Bun.env.MAPTILER_API_KEY || '08nYl6sOIKIAIPMQHGpO';
import jwt from 'jsonwebtoken';

async function login(token, accessCode = null) {
  const jwt_key = process.env.JWT_KEY || "7e96195f-65f6-4df8-b828-9a3457683e53";
  try {
    const decoded = jwt.verify(token, jwt_key);
    return { vendorId: decoded.uid, internalUid: decoded.uid, isHybrid: false, isClientViewEnabled: false };
  } catch (err) {
    throw new Error('Invalid JWT token: ' + err.toString());
  }
}

function cloneHeaders(requestHeaders) {
  const headers = new Headers();
  for (const [key, value] of requestHeaders.entries()) {
    if (key.toLowerCase() !== 'host') {
      headers.append(key, value);
    }
  }
  return headers;
}

async function authenticateRequest(request) {
  const cookieHeader = request.headers.get('cookie') || '';
  const cookies = Object.fromEntries(cookieHeader.split(';').map(cookie => {
    const [name, ...rest] = cookie.split('=');
    return [name.trim(), rest.join('=').trim()];
  }));
  const jwt = cookies.JWT;
  if (!jwt) {
    return { isAuthenticated: false, message: 'Auth Error.' };
  }
  try {
    const authData = await login(jwt);
    if (!authData.vendorId) {
      return { isAuthenticated: false, message: 'Failed to authenticate request.' };
    }
    return { isAuthenticated: true, authData };
  } catch (err) {
    return { isAuthenticated: false, message: err.toString() };
  }
}

console.log('Starting bun server proxy on port 3005...');

Bun.serve({
  port: 3005,
  fetch: async (request) => {
    const url = new URL(request.url);

    // Health check endpoint
    if (url.pathname === '/health') {
      return new Response(JSON.stringify({ status: 'healthy', timestamp: new Date().toISOString() }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const authResult = await authenticateRequest(request);
    if (!authResult.isAuthenticated) {
      return new Response(authResult.message, { status: 401 });
    } 

    if (url.pathname.startsWith('/mapbox')) {
      const pathname = url.pathname.replace('/mapbox', '') || '/';
      const targetUrl = new URL(`https://api.mapbox.com${pathname}`);


      url.searchParams.forEach((value, key) => {
        targetUrl.searchParams.append(key, value);
      });


      if (!targetUrl.searchParams.has('access_token') && mapboxApiKey) {
        targetUrl.searchParams.set('access_token', mapboxApiKey);
      }

      console.log(`Proxying Mapbox request to: ${targetUrl.toString()}`);

      return fetch(targetUrl.toString(), {
        method: request.method,
        headers: cloneHeaders(request.headers),
        body: request.body
      });
    }


    if (url.pathname.startsWith('/maptiler')) {
      const pathname = url.pathname.replace('/maptiler', '') || '/';
      const targetUrl = new URL(`https://api.maptiler.com${pathname}`);


      url.searchParams.forEach((value, key) => {
        targetUrl.searchParams.append(key, value);
      });


      if (!targetUrl.searchParams.has('key') && maptilerApiKey) {
        targetUrl.searchParams.set('key', maptilerApiKey);
      }

      console.log(`Proxying Maptiler request to: ${targetUrl.toString()}`);

      return fetch(targetUrl.toString(), {
        method: request.method,
        headers: cloneHeaders(request.headers),
        body: request.body
      });
    }


    return new Response('Not Found', { status: 404 });
  },
});
