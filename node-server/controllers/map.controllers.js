const jwt = require('jsonwebtoken');
const mysql = require('../utils/db')
;
const aspireAPIService = require('../services/external/aspire-api-service');
const maps = require('../services/map.services')
const {validateVendorAccessCode,UserPrivilege,login} = require("../utils/vendor")
const turf = require('@turf/turf');
const {login: apiLogin} = require('../services/api/account-services');
const fs = require('fs').promises;
const {redisClient} = require('../utils/redis-client.js');

var PHPUnserialize = require('php-unserialize');
const moment = require("moment");


const saveMap = async(req, res,next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT, req.body?.accessCode);
    let result = await maps.saveMap(req.body, vendorId, internalUid);
    res.status(200).send(result);
  } catch (error) {
    next(error);
  }
}

//Direct conversion of PHP code
const openMapLayer = async (req, res, next) => {
  try {
    const accessCode = req.body.accessCode;
    const LayerID = req.body.LayerID;
    const AutoID = req.body.AutoID || "";
    const token = req.cookies.JWT;

    const { vendorId } = await login(token, accessCode);
    if (!vendorId) {
      return res.sendStatus(401);
    }

    let qry;
    let params;
    if (AutoID === "") {
      qry = `SELECT sml_id, sml_layer_data, sml_urlkey FROM sitefotos_map_layers WHERE sml_vendor_id = ? AND sml_id = ?`;
      params = [vendorId, LayerID];
    } else {
      qry = `SELECT sitefotos_map_layers.sml_id AS sml_id, sitefotos_map_autolayers.smlsub_layer_data AS sml_layer_data, sitefotos_map_layers.sml_urlkey AS sml_urlkey FROM sitefotos_map_layers LEFT JOIN sitefotos_map_autolayers ON sitefotos_map_layers.sml_id = sitefotos_map_autolayers.smlsub_layer_id WHERE sitefotos_map_layers.sml_id = ? AND sitefotos_map_layers.sml_vendor_id = ? AND sitefotos_map_autolayers.smlsub_auto_id = ?`;
      params = [LayerID, vendorId, AutoID];
    }

    const result = await mysql.awaitSafeQuery(qry, params, { useMainPool: true });
    if (result && result.length > 0 && result[0].sml_id > 0) {
      const bid = result[0].sml_layer_data;
      let temp1 = PHPUnserialize.unserialize(bid);
      if (temp1 === false) {
        temp1 = trim(substr(bid, strpos(bid, '"') + 1));
        temp1 = substr(temp1, 0, -2);
      }
      const temp2 = JSON.stringify(temp1);
      const urlkey = result[0].sml_urlkey;
      return res.send(`${temp2} ${urlkey}`);
    }
    return res.status(404).send("Map layer not found");
  } catch (error) {
    next(error);
  }
}

const openMapLayerNew = async (req, res, next) => {
  let vendorId;
  try {
    vendorId = (await login(req.cookies.JWT)).vendorId;
    if (!vendorId) {
      return res.sendStatus(401);
    }
    const LayerID = req.params.layer_id;

    const qry = `
      SELECT sml_id, sml_layer_data, sml_urlkey, sml_building_id, sml_layer_name
      FROM sitefotos_map_layers
      WHERE sml_vendor_id = ? AND sml_id = ?`;
    const result = await mysql.awaitSafeQuery(qry, [vendorId, LayerID], { useMainPool: true });

    if (result && result.length > 0) {
      const bid = result[0].sml_building_id;
      const urlkey = result[0].sml_urlkey;
      const layerData = PHPUnserialize.unserialize(result[0].sml_layer_data);

      return res.send({ layerData, urlkey, bid, name: result[0].sml_layer_name });
    } else {
      throw new Error("No data found");
    }
  } catch (error) {
    console.log('NasirProbe', vendorId, error)
    next(error);
  }
};

const getSavedMapLayersTabulator2= async (req, res, next) => {
  try {
    const accessCode = req.body.accessCode || req.query.accessCode;
    const token = req.cookies.JWT;
    const {vendorId} = await login(token, accessCode);
    if (!vendorId) {
      return res.sendStatus(401);
    }

    if (req.body.options){
      //Get sites as well
      const sites = await mysql.awaitSafeQuery(
        "SELECT mb_id, mb_nickname, mb_address1, mb_zip_code, mb_geo, mb_lat, mb_long, (SELECT City FROM maptile_city WHERE CityId = mb_city) AS cityname, (SELECT state_abv_name FROM maptile_state WHERE id = mb_state) AS statename FROM maptile_building WHERE mb_user_id = ? AND mb_user_type = '1' AND mb_status = '1' ORDER BY mb_nickname",
        [vendorId]
      );

      const query = /*SQL*/`SELECT CONCAT(vendor_fname, ' ', vendor_lname) as Vendor, sml_address1 as Address ,sml_city as City, sml_state as State, sml_zip_code as Zip, sml_layer_name AS LayerName, sml_urlkey, sml_id AS LayerID, sml_created_at as CreatedTime, sml_modified_at as ModifiedTime, sml_subuser_creator as CreatorID, sml_subuser_updater as UpdaterID, IF(sml_subuser_creator = 0, CONCAT(vendor_fname, ' ', vendor_lname), IF(sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT(sf_contact_fname, ' ', sf_contact_lname) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) )) as CreatorName, IF(sml_subuser_updater = 0, CONCAT(vendor_fname, ' ', vendor_lname), IF(sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT(sf_contact_fname, ' ', sf_contact_lname) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) )) as UpdaterName, sml_created AS DateTime, 0 AS AutoSave, NULL as AutoID, sml_building_id AS BuildingID FROM sitefotos_map_layers left outer join maptile_vendors on sitefotos_map_layers.sml_vendor_id = maptile_vendors.vendor_id WHERE sml_vendor_id = ? AND sml_active = '1'
                            UNION SELECT sml_vendor_id, sml_address1 as Address ,sml_city as City, sml_state as State, sml_zip_code as Zip, sml_layer_name AS LayerName, sml_urlkey, sml_id AS LayerID, sml_created_at as CreatedTime, sml_modified_at as ModifiedTime, sml_subuser_creator as CreatorID, sml_subuser_updater as UpdaterID, IF(sml_subuser_creator = 0, sml_vendor_id, IF(sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT(sf_contact_fname, ' ', sf_contact_lname) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) )) as CreatorName, IF(sml_subuser_updater = 0, sml_vendor_id, IF(sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT(sf_contact_fname, ' ', sf_contact_lname) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) )) as UpdaterName, smlsub_created AS DateTime, 1 AS AutoSave, sitefotos_map_autolayers.smlsub_auto_id AS AutoID, sml_building_id AS BuildingID FROM sitefotos_map_layers LEFT outer JOIN sitefotos_map_autolayers on sml_id = smlsub_layer_id WHERE sml_vendor_id = ? AND sml_active = '1' AND smlsub_layer_id IS NOT NULL`;
      let maps = await mysql.awaitSafeQuery(query, [vendorId,vendorId]);
      //Server Side
      const { sortBy, sortDesc, page, itemsPerPage } = req.body.options;
      const total = maps.length;

      if (sortBy.length === 1 && sortDesc.length === 1) {
        maps = maps.sort((a, b) => {
          const sortA = a[sortBy[0]]
          const sortB = b[sortBy[0]]

          if (sortDesc[0]) {
            if (sortA < sortB) return 1
            if (sortA > sortB) return -1
            return 0
          } else {
            if (sortA < sortB) return -1
            if (sortA > sortB) return 1
            return 0
          }
        })
      }



      let mapFilters = req.body.filters;
      //Implement filters
      if (mapFilters.LayerName)
        if (mapFilters.LayerName.length > 0) {
          maps = maps.filter(item => item.LayerName ? item.LayerName.toLowerCase().includes(mapFilters.LayerName.toLowerCase()) : false);
        }

      if (mapFilters.Address)
        if (mapFilters.Address.length > 0) {
          maps = maps.filter(item => item.Address ? item.Address.toLowerCase().includes(mapFilters.Address.toLowerCase()) : false);
        }

      if (mapFilters.City)
        if (mapFilters.City.length > 0) {
          maps = maps.filter(item => item.City ? item.City.toLowerCase().includes(mapFilters.City.toLowerCase()) : false);
        }

      if (mapFilters.Zip)
        if (mapFilters.Zip.length > 0) {
          maps = maps.filter(item => item.Zip ? item.Zip.toLowerCase().includes(mapFilters.Zip.toLowerCase()) : false);
        }

      if (mapFilters.sitename)
        if (mapFilters.sitename.length > 0) {
          let filteredSite = sites.filter(({mb_nickname}) => mb_nickname.toLowerCase().includes(mapFilters.sitename.toLowerCase()));
          maps = maps.filter(map => {
            return filteredSite.some(site => site.mb_id === map.BuildingID);
          });
        }

      if (mapFilters.CreatorName)
        if (mapFilters.CreatorName.length > 0) {
          maps = maps.filter(item => item.CreatorName ? item.CreatorName.toLowerCase().includes(mapFilters.CreatorName.toLowerCase()) : false);
        }

      if (mapFilters.UpdaterName)
        if (mapFilters.UpdaterName.length > 0) {
          maps = maps.filter(item => item.UpdaterName ? item.UpdaterName.toLowerCase().includes(mapFilters.UpdaterName.toLowerCase()) : false);
        }

      if (itemsPerPage > 0) {
        maps = maps.slice((page - 1) * itemsPerPage, page * itemsPerPage);
      }
      res.status(200).send({
        maps: {data: maps, count: total},
        sites: {data: sites}
      });
    } else if (req.query){
      res.json([]);
    } else {
      res.json([]);
    }


  } catch (error) {
    console.error(error);
    res.sendStatus(500);
  }
};
const getSavedMapLayers= async (req, res, next) => {
  try {
    const accessCode = req.body.accessCode || req.query.accessCode;
    const token = req.cookies.JWT;
    const {vendorId} = await login(token, accessCode);
      if (!vendorId) {
          return res.sendStatus(401);
      }

    const query = /*SQL*/`SELECT CONCAT(vendor_fname, ' ', vendor_lname) as Vendor, sml_address1 as Address ,sml_city as City, sml_state as State, sml_zip_code as Zip, sml_layer_name AS LayerName, sml_urlkey, sml_id AS LayerID, sml_created_at as CreatedTime, sml_modified_at as ModifiedTime, sml_subuser_creator as CreatorID, sml_subuser_updater as UpdaterID, IF(sml_subuser_creator = 0, CONCAT(vendor_fname, ' ', vendor_lname), IF(sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT(sf_contact_fname, ' ', sf_contact_lname) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) )) as CreatorName, IF(sml_subuser_updater = 0, CONCAT(vendor_fname, ' ', vendor_lname), IF(sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT(sf_contact_fname, ' ', sf_contact_lname) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) )) as UpdaterName, sml_created AS DateTime, 0 AS AutoSave, NULL as AutoID, sml_building_id AS BuildingID FROM sitefotos_map_layers left outer join maptile_vendors on sitefotos_map_layers.sml_vendor_id = maptile_vendors.vendor_id WHERE sml_vendor_id = ? AND sml_active = '1' 
                          UNION SELECT sml_vendor_id, sml_address1 as Address ,sml_city as City, sml_state as State, sml_zip_code as Zip, sml_layer_name AS LayerName, sml_urlkey, sml_id AS LayerID, sml_created_at as CreatedTime, sml_modified_at as ModifiedTime, sml_subuser_creator as CreatorID, sml_subuser_updater as UpdaterID, IF(sml_subuser_creator = 0, sml_vendor_id, IF(sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT(sf_contact_fname, ' ', sf_contact_lname) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) )) as CreatorName, IF(sml_subuser_updater = 0, sml_vendor_id, IF(sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT(sf_contact_fname, ' ', sf_contact_lname) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) )) as UpdaterName, smlsub_created AS DateTime, 1 AS AutoSave, sitefotos_map_autolayers.smlsub_auto_id AS AutoID, sml_building_id AS BuildingID FROM sitefotos_map_layers LEFT outer JOIN sitefotos_map_autolayers on sml_id = smlsub_layer_id WHERE sml_vendor_id = ? AND sml_active = '1' AND smlsub_layer_id IS NOT NULL`;

    const result = await mysql.awaitSafeQuery(query, [vendorId,vendorId]);
    res.json(result);
  } catch (error) {
    console.error(error);
    res.sendStatus(500);
  }
};

const saveMapbyAccessCode = async (req, res) => {
  try {
    const vendorInfo = await validateVendorAccessCode(req.body.accessCode);
    if (vendorInfo.userType === UserPrivilege.InvalidAccessCode) {
      return res.status(403).send("HTTP/1.1 403 Authentication Failure");
    }
    let result = await maps.saveMap(req.body, vendorInfo.vendorId, 0);
    res.status(200).send(result);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}

const backgroundUpdateMeasurements = async(req, res) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let result = maps.backgroundUpdateMeasurements();
    res.status(200).send("Bakcground process started");
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}

const backgroundUpdateBounds = async(req, res) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let result = maps.backgroundUpdateBounds();
    res.status(200).send("Bakcground process started");
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}

const backgroundUpdateScreenshots = async(req, res) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let result = maps.backgroundUpdateScreenshots();
    res.status(200).send("Bakcground process started");
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}
const plotPhotoMap = async(req, res, next) => {
  try {
    maps.createPhotoMap(req.body)
    res.status(200).send("Success")
  } catch (ex) {
    console.log("goo");
  }
}
const copySingleMap = async(req, res) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let map = req.body.map;
    let mapName = req.body.map_name;
    map = map.LayerID
    //We are using same method that is used for copying of multiple maps.
    let data = {
      vendor: vendorId,
      maps: [map]
    };
    console.log("Copy", data)
    let resp = await maps.copyMaps(data, mapName);
    console.log("Copy Map", resp);
    res.status(200).send("process started")

  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}
const copyMaps = async(req, res) => {
  try {
    if(!req.body.password || !req.body.maps || !req.body.vendor) {
      res.status(500).send("Required parameters missing")
      return;
    }

    if (req.body.password !== 'Muj56f44f9') {
      res.status(401).send("Invalid Password")
      return;
    }
    maps.copyMaps(req.body)
    res.status(200).send("process started")

  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}

const convertSiteRecon = async(req, res) => {
  try {
    if(!req.body.password || !req.body.maps || !req.body.vendor) {
      res.status(500).send("Required parameters missing")
      return;
    }

    if (req.body.password !== 'Muj56f44f9') {
      res.status(401).send("Invalid Password")
      return;
    }
    maps.siteRecon2Sitefotos(req.body)
    res.status(200).send("process started")

  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}

const convertAttentive = async(req, res) => {
  try {
    if(!req.query.key || !req.query.username || !req.query.password || !req.query.vendor) {
      res.status(500).send("Required parameters missing")
      return;
    }

        if (req.query.key !== 'Muj56f44f9') {
            res.status(401).send("Invalid Password")
            return;
        }

        maps.attentiveDownload(req.query.username, req.query.password, req.query.vendor, req.query?.redownload)
        res.status(200).send("process started")

  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}


const convertAttentiveSelective = async(req, res) => {
    try {
        if(!req.body) { req.status(500).send("Required Parameters missing"); return;}
        if(!req.body.key || !req.body.username || !req.body.password || !req.body.vendor || !req.body.data) {
            res.status(500).send("Required parameters missing")
            return;
        }



        if (req.body.key !== 'Muj56f44f9') {
            res.status(401).send("Invalid Password")
            return;
        }

        maps.attentiveDownloadSelective(req.body.username, req.body.password, req.body.vendor, req.body.data, req.body?.redownload)
        res.status(200).send("process started")

    } catch (error) {
        res.status(500).send({ message: error.message });
    }
}

const convertGoilawn = async(req, res) => {
  try {
    if(!req.query.key || !req.query.username || !req.query.password || !req.query.vendor) {
      res.status(500).send("Required parameters missing")
      return;
    }

    if (req.query.key !== 'Muj56f44f9') {
      res.status(401).send("Invalid Password")
      return;
    }
    maps.goilawnDownload(req.query.username, req.query.password, req.query.vendor)
    res.status(200).send("process started")

  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}

const convertSiteRecon2 = async(req, res) => {
  try {
    if(!req.query.key || !req.query.username || !req.query.password || !req.query.vendor) {
      res.status(500).send("Required parameters missing")
      return;
    }
    let reDownload = req.query.redownload || false;
    if (req.query.key !== 'Muj56f44f9') {
      res.status(401).send("Invalid Password")
      return;
    }
    maps.siteReconDownload(req.query.username, req.query.password, req.query.vendor, reDownload)
    res.status(200).send("process started")

  } catch (error) {
    res.status(500).send({ message: error.message });
  }
}

const transferMaps = async (req, res) => {
  try {
    if (req.body.password) {
      if (req.body.password !== 'Muj56f44f9') {
        res.status(401).send("Invalid Password")
        return;
      }
      if(!req.body.maps)
      {
        res.status(500).send("Required parameters missing")
        return;
      }
      const vendorEmail = req.body.vendor_email;
      const vendorId = req.body.vendor;
      let vendorResult;
      if (!vendorEmail && !vendorId) {
        res.status(400).json({ error: "Either vendor email or vendorId is required." });
        return;
      }

      if (vendorId) {
        vendorResult = await mysql.awaitQuery("select vendor_id, vendor_access_code from maptile_vendors where vendor_id=?",[vendorId]);
      } else if (vendorEmail) {
        vendorResult = await mysql.awaitQuery("select vendor_id, vendor_access_code from maptile_vendors where vendor_email=?",[vendorEmail]);
      }

      if(!vendorResult || !vendorResult[0])
      {
        res.status(500).send("Vendor not found")
        return;
      }
      res.status(202).json({ status: "Processing data in the background" });

      if (req.body.maps) {
        if (Array.isArray(req.body.maps)) {
          let images = [];
          for (let map of req.body.maps) {
            const result = await mysql.awaitQuery("select * from sitefotos_map_layers where sml_id = ?", [map]);
            if (result && result[0]) {
              try {
                let layerData = JSON.parse(PHPUnserialize.unserialize(result[0]['sml_layer_data']));
                if (typeof layerData[0] != 'undefined') {
                  if (typeof layerData[0].metadata !== 'undefined') {
                    if (typeof layerData[0].metadata.baselayer !== 'undefined') {
                      images.push(layerData[0].metadata.image)
                    }
                  }
                }
                let condition = true;
                let urlkey = '';
                while(condition)
                {
                  urlkey = Array.from(Array(6), () => Math.floor(Math.random() * 36).toString(36)).join('');
                  const testKey = await mysql.awaitQuery("SELECT sml_urlkey FROM sitefotos_map_layers  WHERE sml_urlkey=?",[urlkey])
                  if(!testKey || !testKey[0])
                    condition = false;
                }


                const newMap = {
                  sml_created: Math.floor(new Date().getTime() / 1000),
                  sml_vendor_id: vendorResult[0]['vendor_id'],
                  sml_layer_name: result[0]['sml_layer_name'],
                  sml_layer_data: result[0]['sml_layer_data'],
                  sml_urlkey: urlkey,
                  sml_address1: result[0]['sml_address1'],
                  sml_address2: result[0]['sml_address2'],
                  sml_city: result[0]['sml_city'],
                  sml_state: result[0]['sml_state'],
                  sml_zip_code: result[0]['sml_zip_code'],
                  sml_country: result[0]['sml_country'],
                  sml_measurements: result[0]['sml_measurements'],
                  sml_pt_orderid_reference: result[0]['sml_pt_orderid'],
                  sml_active: '1',
                  sml_isauto: 0
                };


                const insertedRecord = await mysql.insertObj('sitefotos_map_layers', newMap);

                let boundsPoly = await maps.getMapBounds(insertedRecord.insertId);
                let centroid = turf.centroid(boundsPoly);
                if (boundsPoly) {
                  await mysql.awaitQuery(/*SQL*/`UPDATE sitefotos_map_layers SET sml_bounds = ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON(?)), 4326), sml_center=ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON(?)), 4326) where sml_id=?`, [JSON.stringify(boundsPoly.geometry), JSON.stringify(centroid.geometry), insertedRecord.insertId]);
                }
                maps.uploadScreenshot(urlkey);

              } catch (ex) {
                console.log(ex)
                continue;
              }
              // console.log(layerData)
            }
          }
          for(let image of images)
          {
            try{
              let imageObject = {
                'url': image,
                'accesscode': vendorResult[0]['vendor_access_code'],
                'password': 'Muj56f44f9',
              };
              const copyUrl = `https://tiles.sitefotos.com/api2/general/copytile`;


              await fetch(copyUrl, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(imageObject)
              });
            } catch(ex)
            {
              console.log(ex)
              continue;
            }
          }
          console.log(images)
        }
      }
    }
    else {
      res.status(401).send("Invalid Password")
      return;
    }

  } catch (ex) {
    console.log(ex)
    res.status(500).send("Error")
  }
}

const transferMapsPto = async (req, res) => {
    try {
        //sml_pt_orderid will store order ids from pto
        //sml_pt_orderid_reference will store old pt ids
        //return sml_id, pt_order_id and sml_urlkey back to pto
        //sml_pt_client_id is id of the worker
        //PTO will send accessCode and here we will convert it to vendor
        if (req.body.password) {
            if (req.body.password !== 'Muj56f44f9') {
                res.status(401).send("Invalid Password")
                return;
            }
            if(!req.body.vendor_access_code || !req.body.maps)
            {
                res.status(500).send("Required parameters missing")
                return;
            }
            let accessCode = req.body.vendor_access_code;
            let vendorInfo = await validateVendorAccessCode(accessCode);

            if (vendorInfo.userType === UserPrivilege.InvalidAccessCode) {
                res.status(500).send("Vendor not found");
                return
            }
            let vendorId = vendorInfo.vendorId;
            const vendorResult = await mysql.awaitQuery("select vendor_access_code from maptile_vendors where vendor_id=?",[vendorId])
            if(!vendorResult || !vendorResult[0])
            {
                res.status(500).send("Vendor not found")
                return;
            }

            let responseForPto = [];
            if (req.body.maps) {
                if (Array.isArray(req.body.maps)) {
                    let images = [];
                    for (let map of req.body.maps) {
                        let reference_id = map.reference_id;
                        let new_map_id = map.new_map_id;
                        const result = await mysql.awaitQuery("select * from sitefotos_map_layers where sml_pt_orderid = ?", [reference_id]);
                        if (result && result[0]) {
                            try {
                                let layerData = JSON.parse(PHPUnserialize.unserialize(result[0]['sml_layer_data']));
                                if (typeof layerData[0] != 'undefined') {
                                    if (typeof layerData[0].metadata !== 'undefined') {
                                        if (typeof layerData[0].metadata.baselayer !== 'undefined') {
                                            images.push(layerData[0].metadata.image)
                                        }
                                    }
                                }
                                let condition = true;
                                let urlkey = '';

                                while(condition)
                                {
                                    urlkey = Array.from(Array(6), () => Math.floor(Math.random() * 36).toString(36)).join('');
                                    const testKey = await mysql.awaitQuery("SELECT sml_urlkey FROM sitefotos_map_layers  WHERE sml_urlkey=?",[urlkey])
                                    if(!testKey || !testKey[0])
                                        condition = false;
                                }


                                const newMap = {
                                    sml_created: Math.floor(new Date().getTime() / 1000),
                                    sml_vendor_id: vendorId,
                                    sml_layer_name: result[0]['sml_layer_name'],
                                    sml_layer_data: result[0]['sml_layer_data'],
                                    sml_urlkey: urlkey,
                                    sml_address1: result[0]['sml_address1'],
                                    sml_address2: result[0]['sml_address2'],
                                    sml_city: result[0]['sml_city'],
                                    sml_state: result[0]['sml_state'],
                                    sml_zip_code: result[0]['sml_zip_code'],
                                    sml_country: result[0]['sml_country'],
                                    sml_measurements: result[0]['sml_measurements'],
                                    sml_pt_orderid: new_map_id,
                                    sml_pt_orderid_reference: reference_id,
                                    sml_active: '1',
                                    sml_isauto: 0
                                };


                                const insertedRecord = await mysql.insertObj('sitefotos_map_layers', newMap);

                                let boundsPoly = await maps.getMapBounds(insertedRecord.insertId);
                                let centroid = turf.centroid(boundsPoly);
                                if (boundsPoly) {
                                    await mysql.awaitQuery(/*SQL*/`UPDATE sitefotos_map_layers SET sml_bounds = ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON(?)), 4326), sml_center=ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON(?)), 4326) where sml_id=?`, [JSON.stringify(boundsPoly.geometry), JSON.stringify(centroid.geometry), insertedRecord.insertId]);
                                }
                                maps.uploadScreenshot(urlkey);
                                responseForPto.push({
                                    sml_id: insertedRecord.insertId,
                                    sml_urlkey: newMap.sml_urlkey,
                                    sml_pt_orderid_reference: newMap.sml_pt_orderid_reference,
                                    sml_pt_orderid: new_map_id
                                })
                            } catch (ex) {
                                console.log(ex)
                                continue;
                            }
                        }
                    }
                    for(let image of images)
                    {
                        try{
                            let imageObject = {
                                'url': image,
                                'accesscode': vendorResult[0]['vendor_access_code'],
                                'password': 'Muj56f44f9',
                            };
                            const copyUrl = `https://tiles.sitefotos.com/api2/general/copytile`;


                            await fetch(copyUrl, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(imageObject)
                            });
                        } catch(ex)
                        {
                            console.log(ex)
                            continue;
                        }
                    }
                }
            }
            res.status(200).json(responseForPto);
        }
        else {
            res.status(401).send("Invalid Password")
            return;
        }

    } catch (ex) {
        console.log(ex)
        res.status(500).send("Error")
    }
}

//Todo: get rid of this amad
const transferMapsPtoInternalSitefotosMaps = async (req, res) => {
    try {
        //sml_pt_orderid will store order ids from pto
        //sml_pt_orderid_reference will store old pt ids
        //return sml_id, pt_order_id and sml_urlkey back to pto
        //sml_pt_client_id is id of the worker
        //PTO will send accessCode and here we will convert it to vendor
        if (req.body.password) {
            if (req.body.password !== 'Muj56f44f9') {
                res.status(401).send("Invalid Password")
                return;
            }
            if(!req.body.vendor_access_code || !req.body.maps)
            {
                res.status(500).send("Required parameters missing")
                return;
            }
            let accessCode = req.body.vendor_access_code;
            let vendorInfo = await validateVendorAccessCode(accessCode);

            if (vendorInfo.userType === UserPrivilege.InvalidAccessCode) {
                res.status(500).send("Vendor not found");
                return
            }
            let vendorId = vendorInfo.vendorId;

            let responseForPto = [];
            if (req.body.maps) {
                if (Array.isArray(req.body.maps)) {
                    let images = [];
                    for (let map of req.body.maps) {
                        let reference_id = map.reference_id;
                        let new_map_id = map.new_map_id;
                        const result = await mysql.awaitQuery("select * from sitefotos_map_layers where sml_id = ?", [reference_id]);
                        if (result && result[0]) {
                            try {
                                let layerData = JSON.parse(PHPUnserialize.unserialize(result[0]['sml_layer_data']));
                                if (typeof layerData[0] != 'undefined') {
                                    if (typeof layerData[0].metadata !== 'undefined') {
                                        if (typeof layerData[0].metadata.baselayer !== 'undefined') {
                                            images.push(layerData[0].metadata.image)
                                        }
                                    }
                                }
                                let condition = true;
                                let urlkey = '';
                                while(condition)
                                {
                                    urlkey = Array.from(Array(6), () => Math.floor(Math.random() * 36).toString(36)).join('');
                                    const testKey = await mysql.awaitQuery("SELECT sml_urlkey FROM sitefotos_map_layers  WHERE sml_urlkey=?",[urlkey])
                                    if(!testKey || !testKey[0])
                                        condition = false;
                                }


                                const newMap = {
                                    sml_created: Math.floor(new Date().getTime() / 1000),
                                    sml_vendor_id: vendorId,
                                    sml_layer_name: result[0]['sml_layer_name'],
                                    sml_layer_data: result[0]['sml_layer_data'],
                                    sml_urlkey: urlkey,
                                    sml_address1: result[0]['sml_address1'],
                                    sml_address2: result[0]['sml_address2'],
                                    sml_city: result[0]['sml_city'],
                                    sml_state: result[0]['sml_state'],
                                    sml_zip_code: result[0]['sml_zip_code'],
                                    sml_country: result[0]['sml_country'],
                                    sml_measurements: result[0]['sml_measurements'],
                                    sml_pt_orderid: new_map_id,
                                    sml_pt_orderid_reference: result[0]['sml_pt_orderid'],
                                    sml_active: '1',
                                    sml_isauto: 0
                                };


                                const insertedRecord = await mysql.insertObj('sitefotos_map_layers', newMap);

                                let boundsPoly = await maps.getMapBounds(insertedRecord.insertId);
                                let centroid = turf.centroid(boundsPoly);
                                if (boundsPoly) {
                                    await mysql.awaitQuery(/*SQL*/`UPDATE sitefotos_map_layers SET sml_bounds = ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON(?)), 4326), sml_center=ST_GeomFromText(ST_AsText(ST_GeomFromGeoJSON(?)), 4326) where sml_id=?`, [JSON.stringify(boundsPoly.geometry), JSON.stringify(centroid.geometry), insertedRecord.insertId]);
                                }
                                maps.uploadScreenshot(urlkey);
                                responseForPto.push({
                                    sml_id: insertedRecord.insertId,
                                    sml_urlkey: newMap.sml_urlkey,
                                    sml_pt_orderid_reference: newMap.sml_pt_orderid_reference,
                                    sml_pt_orderid: new_map_id
                                })
                            } catch (ex) {
                                console.log(ex)
                                continue;
                            }
                            console.log(responseForPto)
                        }
                    }
                    for(let image of images)
                    {
                        try{
                            let imageObject = {
                                'url': image,
                                'accesscode': vendorResult[0]['vendor_access_code'],
                                'password': 'Muj56f44f9',
                            };
                            const copyUrl = `https://tiles.sitefotos.com/api2/general/copytile`;


                            await fetch(copyUrl, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(imageObject)
                            });
                        } catch(ex)
                        {
                            console.log(ex)
                            continue;
                        }
                    }
                }
            }
            res.status(200).json(responseForPto);
        }
        else {
            res.status(401).send("Invalid Password")
            return;
        }

    } catch (ex) {
        console.log(ex)
        res.status(500).send("Error")
    }
}

const getMapLayerData = async (req, res) => {
  let { vendorId }= await login(req.cookies.JWT)
  if(!vendorId) {
    console.error("Invalid Access Code");
    res.status(401).send("Invalid Access Code");
    return
  }
  //const results = await mysql.awaitQuery("select sml_layer_data from sitefotos_map_layers where sml_vendor_id = ? and sml_urlkey", [vendorId, req.query.sml_urlkey]);
  const results = await mysql.awaitQuery("select sml_layer_data from sitefotos_map_layers where sml_urlkey = ?", [req.query.sml_urlkey]);
  if(!results || results.length == 0){
    res.status(404).send("Couldnt find map");
    return
  }
  let layData = JSON.parse(PHPUnserialize.unserialize(results[0]['sml_layer_data']));
  res.status(200).send(layData);
}

const getAspireSites = async(req, res) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    const result = await aspireAPIService.getSitesDetails(vendorId)
    res.json(result);
  } catch (err) {
    console.error("Error in getAspireSites", err);
    res.status(500).send(err.message);
  }
}

const getAspireTakeoffs = async(req, res) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    const result = await aspireAPIService.getTakeoffs(vendorId)
    res.json(result);
  } catch (err) {
    console.error("Error in getAspireTakeoffs", err);
    res.status(500).send(err.message);
  }
}

const getDefaultAssociations = async(req, res) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    const result = await aspireAPIService.getDefaultAssociations(vendorId)
    res.json(result);
  } catch (err) {
    console.error("Error in getDefaultAssociations", err);
    res.status(500).send(err.message);
  }
}

const pushAspireTakeoffs = async(req, res) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    const result = await aspireAPIService.pushTakeoffs(vendorId, req.body)
    res.json(result);
  } catch (err) {
    console.error("Error in getAspireTakeoffs", err);
    res.status(500).send(err.message);
  }
}

const getAppleToken = async (req,res) => {

  try {
    const { vendorId } = await login(req.cookies.JWT);
    const result = await maps.createAppleToken();
    res.json(result);
  } catch (err) {
    console.error("Error in getAppleToken", err);
    res.status(500).send(err.message);
  }
}

const test = async (req, res) => {

  try {
    const result = await mysql.awaitQuery("select 1;", []);
    res.json(result);
  } catch (err) {
    console.error("Error in /estimator/test", err);
    res.status(500).send(err.message);
  }

}

const mapbuilderLink = async (req, res, next) => {
    try {


        let {vendor: vendorID, unrestricted} = await apiLogin(req.query.apiKey, req.query.accessCode) || {};
        if (vendorID) {
            let groups = await mysql.awaitQuery(/*SQL*/`select smg_id AS groupID, smg_name as groupName, smg_stroke as stroke, smg_weight as weight, smg_strokeOpacity as strokeOpacity, smg_fillColor as fillColor, smg_fillOpacity as fillOpacity, smg_grpcategory as category, smg_measurement_type as measurementType FROM sitefotos_map_groups WHERE smg_vendor_id=?`, [vendorID]);
            const icons = await fs.readFile('./assets/sitefotos_map_icons.json', 'utf8');
            const vendorData = await mysql.awaitQuery(/*SQL*/`select vendor_fname, vendor_lname, vendor_company_logo, vendor_company_name from maptile_vendors where  vendor_id=?`, [vendorID]);
            const permissions = await mysql.awaitQuery(/*SQL*/`select supr_permission_details from sitefotos_user_permissions_roles where supr_id=1`);
            let lat = req.query.lat ?? 40.730610;
            let lng = req.query.lng ?? -73.935242;
            let address = req.query.address ?? null;
            let city = req.query.city ?? null;
            let state = req.query.state ?? null;
            let zip = req.query.zip ?? null;
            let provider = req.query.provider ?? null;
            let mapid = req.query.mapid ?? null;
            if (!provider)
                res.status(500).send("No provider specified")

      const data = {
        myvtem: vendorID,
        myvid: req.query.accessCode,
        globalServerGroups: groups,
        myicons: JSON.parse(icons),
        mycompanylogo: vendorData[0].vendor_company_logo,
        mycompanyname: vendorData[0].vendor_company_name,
        myfname: vendorData[0].vendor_fname,
        mylname: vendorData[0].vendor_lname,
        userpermissions: JSON.parse(permissions[0].supr_permission_details),
        globalExternalFlag: true,
        globalExternalProvider: provider
      }
      if(mapid) {
        const mapData = await mysql.awaitQuery(/*SQL*/`select sml_id, sml_layer_data, sml_layer_name,sml_address1,sml_city,sml_state,sml_zip_code,sml_urlkey,sml_building_id from sitefotos_map_layers where sml_uuid=?`, [mapid]);
        if(mapData.length > 0) {
          data.globalExternalFileID = mapData[0].sml_id;
          data.globalExternalFileName = mapData[0].sml_layer_name;
          data.globalExternalAddress = mapData[0].sml_address1;
          data.globalExternalCity = mapData[0].sml_city;
          data.globalExternalState = mapData[0].sml_state;
          data.globalExternalZip = mapData[0].sml_zip;
          data.globalExternalUrlKey = mapData[0].sml_urlkey;
          data.globalExternalBuildingID = mapData[0].sml_building_id;
          data.globalLayers = PHPUnserialize.unserialize(mapData[0].sml_layer_data);
        }
        else {
          res.status(500).send("Invalid Map ID");
        }
      }
      else {
        if(lat) data.globalExternalLat = lat;
        if(lng) data.globalExternalLon = lng;
        if(address) data.globalExternalAddress = address;
        if(city) data.globalExternalCity = city;
        if(state) data.globalExternalState = state;
        if(zip) data.globalExternalZip = zip;
      }

      res.render('map/mapbuilder-link', { data: data });
    }
    else {
      res.status(500).send("Invalid API Key or Access Code");
    }
  } catch (err) {
    console.error("Error in mapbuilderLink", err);
    next(err)
  }
}

const mapbuilderPT = async (req, res, next) => {
  try {
    res.setHeader('Access-Control-Allow-Origin', '*');
    let { workerId, clientId, geofence, propName, propAddress, propCity, propState, propZip, takeoffs, orderId } = req.body;

    if (!workerId || !clientId || !geofence || !propName || !propAddress || !propCity || !propState || !propZip || !takeoffs) {
      res.status(500).send("Required parameters missing")
      return;
    }
    let workerInfo = await validateVendorAccessCode(workerId);
    let clientInfo = await validateVendorAccessCode(clientId);

    if (workerInfo.userType === UserPrivilege.InvalidAccessCode) {
      res.status(500).send("Worker not found");
      return
    }
    if (clientInfo.userType === UserPrivilege.InvalidAccessCode) {
      res.status(500).send("Worker not found");
      return
    }
    let vendorId = workerInfo.vendorId;

    const icons = await fs.readFile('./assets/sitefotos_map_icons.json', 'utf8');


    const data = {
      icons: JSON.parse(icons),
      myvid: workerId,
      mycompanylogo: "",
      mycompanyname:"",
      myfname: "",
      mylname:"",
      globalTakeoffsBounds: geofence,
      globalTakeoffsPropertyName: propName,
      TakeoffPropertyAddress: propAddress,
      TakeoffPropertyCity: propCity,
      TakeoffPropertyState: propState,
      TakeoffPropertyZip: propZip,
      globalTakeoffs: takeoffs,
      globalTakeoffsOrderID: orderId,
      globalTakeoffClientAccessCode: clientId,
      globalTakeoffsClientVID: clientInfo.vendorId,
      openedFromPto: true
    }
    if (orderId) {
      const mapData = await mysql.awaitQuery(/*SQL*/`select sml_id, sml_layer_data, sml_layer_name,sml_address1,sml_city,sml_state,sml_zip_code,sml_urlkey,sml_building_id from sitefotos_map_layers where sml_pt_orderid=? and sml_vendor_id=?`, [orderId, vendorId]);
      if (mapData.length > 0) {
        data.globalTakeoffsFileName = mapData[0].sml_layer_name;
        data.globalTakeoffsFileID = mapData[0].sml_id;
        data.layers = PHPUnserialize.unserialize(mapData[0].sml_layer_data);
      }
    }

    res.render('map/mapbuilder-pt', { data: data });

  } catch (err) {
    console.error("Error in mapbuilderLink", err);
    next(err)
  }
}

const mapMeasurementsGoogleSheets = async function (req, res, next) {
  try {

    const { vendorId } = await login(req.cookies?.JWT);
    let mapId = req.params.map_id;
    let results = await mysql.awaitQuery(/*SQL*/`select sml_id, ST_X(sml_center) as lng, ST_Y(sml_center) as lat, sml_layer_data, sml_layer_name,IF( sml_subuser_creator = 0, vendor_company_name, IF( sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) ) ) as CreatorName, IF( sml_subuser_updater = 0, vendor_company_name, IF( sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) ) ) as UpdaterName,UNIX_TIMESTAMP(sml_created_at) as CreatedTime, UNIX_TIMESTAMP(sml_modified_at) as ModifiedTime, vendor_company_logo as logo,sml_urlkey, sml_address1, sml_city, sml_zip_code from sitefotos_map_layers left join maptile_vendors on sml_vendor_id=vendor_id where sml_id = ?`, [mapId]);
    if (results && results.length > 0) {
      let row = {};
      let mapData = PHPUnserialize.unserialize(results[0]['sml_layer_data']);
      if (mapData) {

        let featurename = "";
        let featurearea = 0;
        let featureperim = 0;
        let num = 0;
        let featurefullname = ''
        let featurecolor = "";

        if (typeof (mapData) != 'object') {
          mapData = JSON.parse(mapData);
        }
        else {
          let temp = mapData;
          mapData = [];
          mapData.push(temp);
        }

        for (const item of mapData) {

          let mapDataFeatures = Object.values(item.features);
          featurename = "";
          featurearea = 0;
          featureperim = 0;
          featurefullname = '';
          num = 0;
          featurecolor = "";

          for (const feature of mapDataFeatures) {

            if (feature.properties.area && feature.properties.perimeter) {


              let name = item.metadata?.name || feature.properties.preset;
              let color = item.metadata?.fillColor || feature.properties.fillColor;
              featurename = feature.properties.preset;
              featurecolor = color;
              featurefullname = name;
              featurearea += feature.properties.area ? parseInt(feature.properties.area.replace(/[^0-9]/g, '')) : 0;
              featureperim += feature.properties.perimeter ? parseInt(feature.properties.perimeter.replace(/[^0-9]/g, '')) : 0;
              num += 1;
            }



          }

          if (num > 0)
            row[featurename] = { layer_name: featurefullname, layer_area_sqft: featurearea, layer_length: featureperim, layer_count: num, layer_color: featurecolor, layer_acres: parseFloat(featurearea / 43560).toFixed(2) };
        }
        let dataArray = [];

        for (let key in row) {
          if (row.hasOwnProperty(key)) {
            dataArray.push(row[key]);
          }
        }
        let data = {
          map_name: results[0]['sml_layer_name'],
          guest_map_url: "https://www.sitefotos.com/vpics/guestmap?" + results[0]['sml_urlkey'],
          map_coordinates: parseFloat(results[0]['lng']).toFixed(4) + ', ' + parseFloat(results[0]['lat']).toFixed(4),
          created_by: results[0]['CreatorName'],
          last_editor: results[0]['UpdaterName'],
          created_date: moment.unix(results[0]['CreatedTime']).format("MM/DD/YYYY"),
          last_edited_date: moment.unix(results[0]['ModifiedTime']).format("MM/DD/YYYY"),
          logo_url: results[0]['logo'],
          map_address: results[0]['sml_address1'],
          layers: dataArray


        }
        let layerTotal = {
          layer_area_sqft: 0,
          layer_acres: 0,
          layer_length: 0,
          layer_count: 0
        };

        data.layers.forEach(layer => {
          layerTotal.layer_area_sqft += Number(layer.layer_area_sqft);
          layerTotal.layer_acres += Number(layer.layer_acres);
          layerTotal.layer_length += Number(layer.layer_length);
          layerTotal.layer_count += Number(layer.layer_count);
        });


        layerTotal.layer_area_sqft = layerTotal.layer_area_sqft.toString();
        layerTotal.layer_acres = layerTotal.layer_acres.toFixed(2);
        layerTotal.layer_length = layerTotal.layer_length.toString();
        layerTotal.layer_count = layerTotal.layer_count.toString();


        data.layer_total = layerTotal;
        let request = await fetch("https://script.google.com/macros/s/AKfycbwxo6Fux9DLjeiaHWn5_ko-j_caVj5ZdN7GH8XyXBAvu57_9LiGARp-j5J8ag1iZbO1/exec", {
          method: "POST",
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 120000,
          useProxy: true,
          body: JSON.stringify(data)
        })
        if (!request.ok)
          return res.status(500).send("Error");
        let url = await (await request).text();
        return res.send(url);
      }
      return res.status(404).send("Map not found");
    }
    return res.status(404).send("Map not found");




  } catch (ex) {
    next(ex)
  }


}

const mapMeasurementsGoogleSheetsPdf = async function (req, res, next) {
  try {

    const { vendorId } = await login(req.cookies?.JWT);
    let mapId = req.params.map_id;
    let results = await mysql.awaitQuery(/*SQL*/`select sml_id, ST_X(sml_center) as lng, ST_Y(sml_center) as lat, sml_layer_data, sml_layer_name,IF( sml_subuser_creator = 0, vendor_company_name, IF( sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) ) ) as CreatorName, IF( sml_subuser_updater = 0, vendor_company_name, IF( sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) ) ) as UpdaterName,UNIX_TIMESTAMP(sml_created_at) as CreatedTime, UNIX_TIMESTAMP(sml_modified_at) as ModifiedTime, vendor_company_logo as logo,sml_urlkey, sml_address1, sml_city, sml_zip_code from sitefotos_map_layers left join maptile_vendors on sml_vendor_id=vendor_id where sml_id = ?`, [mapId]);
    if (results && results.length > 0) {
      let row = {};
      let mapData = PHPUnserialize.unserialize(results[0]['sml_layer_data']);
      if (mapData) {

        let featurename = "";
        let featurearea = 0;
        let featureperim = 0;
        let num = 0;
        let featurefullname = ''
        let featurecolor = "";

        if (typeof (mapData) != 'object') {
          mapData = JSON.parse(mapData);
        }
        else {
          let temp = mapData;
          mapData = [];
          mapData.push(temp);
        }

        for (const item of mapData) {

          let mapDataFeatures = Object.values(item.features);
          featurename = "";
          featurearea = 0;
          featureperim = 0;
          featurefullname = '';
          num = 0;
          featurecolor = "";

          for (const feature of mapDataFeatures) {

            if (feature.properties.area && feature.properties.perimeter) {


              let name = item.metadata?.name || feature.properties.preset;
              let color = item.metadata?.fillColor || feature.properties.fillColor;
              featurename = feature.properties.preset;
              featurecolor = color;
              featurefullname = name;
              featurearea += feature.properties.area ? parseInt(feature.properties.area.replace(/[^0-9]/g, '')) : 0;
              featureperim += feature.properties.perimeter ? parseInt(feature.properties.perimeter.replace(/[^0-9]/g, '')) : 0;
              num += 1;
            }



          }

          if (num > 0)
            row[featurename] = { layer_name: featurefullname, layer_area_sqft: featurearea, layer_length: featureperim, layer_count: num, layer_color: featurecolor, layer_acres: parseFloat(featurearea / 43560).toFixed(2) };
        }
        let dataArray = [];

        for (let key in row) {
          if (row.hasOwnProperty(key)) {
            dataArray.push(row[key]);
          }
        }
        let data = {
          image_url: "https://capture.sitefotos.com/screenshot?url=http://sitefotos.com/vpics/guestmapscreenshot?" + results[0]['sml_urlkey'],
          map_name: results[0]['sml_layer_name'],
          guest_map_url: "https://www.sitefotos.com/vpics/guestmap?" + results[0]['sml_urlkey'],
          map_coordinates: parseFloat(results[0]['lng']).toFixed(4) + ', ' + parseFloat(results[0]['lat']).toFixed(4),
          created_by: results[0]['CreatorName'],
          last_editor: results[0]['UpdaterName'],
          created_date: moment.unix(results[0]['CreatedTime']).format("MM/DD/YYYY"),
          last_edited_date: moment.unix(results[0]['ModifiedTime']).format("MM/DD/YYYY"),
          logo_url: results[0]['logo'],
          map_address: results[0]['sml_address1'],
          layers: dataArray


        }
        let layerTotal = {
          layer_area_sqft: 0,
          layer_acres: 0,
          layer_length: 0,
          layer_count: 0
        };

        data.layers.forEach(layer => {
          layerTotal.layer_area_sqft += Number(layer.layer_area_sqft);
          layerTotal.layer_acres += Number(layer.layer_acres);
          layerTotal.layer_length += Number(layer.layer_length);
          layerTotal.layer_count += Number(layer.layer_count);
        });


        layerTotal.layer_area_sqft = layerTotal.layer_area_sqft.toString();
        layerTotal.layer_acres = layerTotal.layer_acres.toFixed(2);
        layerTotal.layer_length = layerTotal.layer_length.toString();
        layerTotal.layer_count = layerTotal.layer_count.toString();


        data.layer_total = layerTotal;
        let request = await fetch("https://script.google.com/macros/s/AKfycbzqRi20qK7P-Pie1FuGnWLDuXl_or4IRXESbH1zRXZ5wn5E3nhkbYJUOaTmmEPiM9jxrA/exec", {
          method: "POST",
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 120000,
          useProxy: true,
          body: JSON.stringify(data)
        })
        if (!request.ok)
          return res.status(500).send("Error");
        let url = await (await request).text();
        return res.send(url);
      }
      return res.status(404).send("Map not found");
    }
    return res.status(404).send("Map not found");




  } catch (ex) {
    next(ex)
  }


}

const createSiteMap = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);

    const { parcel, buildingId, buildingName, buildingAddress, buildingCity, buildingState, buildingZip, buildingCountry, layerName} = req.body;
    let returnData = await maps.createSiteMap(parcel, vendorId, buildingId, buildingName, buildingAddress, buildingCity, buildingState, buildingZip, buildingCountry, internalUid, layerName);
    res.send(returnData);
  } catch (error) {
    next(error);
  }
}
const exportMapAsGoogleSheet = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let sheetName = `Maps_Export_${moment().unix()}`;
    let data = req.body.data;
    let sheetUrl = await maps.postToGoogle(
      'https://script.google.com/macros/s/AKfycbwRCAg5YJmwvFdAOwlDNPZRv8lXHN5iI46qzRvkMALn-A7mWRnigZPHAPbgS3si9-kLIQ/exec',
      vendorId,
      sheetName,
      data
    );
    res.send(sheetUrl);
  } catch (error) {
    next(error);
  }
}
const getMapsTabulator = async (req, res, next) => {
  try {
    const accessCode = req.body.accessCode || req.query.accessCode;
    const token = req.cookies.JWT;
    const { vendorId, internalUid, restrictedUser, userContactId } = await login(token);
    if (!vendorId) {
      return res.sendStatus(401);
    }
    // NOTE: Default size removed from here to correctly capture requestedSize later
    const { filter = [], sort = [] } = req.body;

    let cacheBust = req.body.cacheBust ?? false;

    let id = `${vendorId}-${internalUid}`;

    const cache = await redisClient.get(`GET_MAPS_TABULATOR:${id}`);

    let maps = [];
    if (cache && !cacheBust)
      maps = JSON.parse(cache);
    else {

      let query = /*SQL*/`SELECT CONCAT(vendor_fname, ' ', vendor_lname) as Vendor, sml_address1 as Address, sml_city as City, sml_state as State, sml_zip_code as Zip, sml_layer_name AS LayerName, sml_urlkey, sml_id AS LayerID, UNIX_TIMESTAMP(sml_created_at) as CreatedTime, UNIX_TIMESTAMP(sml_modified_at) as ModifiedTime, sml_subuser_creator as CreatorID, sml_subuser_updater as UpdaterID, IF( sml_subuser_creator = 0, CONCAT(vendor_fname, ' ', vendor_lname), IF( sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) ) ) as CreatorName, IF( sml_subuser_updater = 0, CONCAT(vendor_fname, ' ', vendor_lname), IF( sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) ) ) as UpdaterName, sml_created AS DateTime, 0 AS AutoSave, NULL as AutoID, sml_building_id AS BuildingID, mb_nickname as BuildingName FROM sitefotos_map_layers left outer join maptile_vendors on sitefotos_map_layers.sml_vendor_id = maptile_vendors.vendor_id left outer join maptile_building on sitefotos_map_layers.sml_building_id = mb_id WHERE sml_vendor_id = ? AND sml_active = '1'${restrictedUser && userContactId>0 ? `AND (mb_manager = ${userContactId} OR mb_manager_secondary =${userContactId} OR mb_manager_third =${userContactId})` : ''} UNION SELECT sml_vendor_id, sml_address1 as Address, sml_city as City, sml_state as State, sml_zip_code as Zip, sml_layer_name AS LayerName, sml_urlkey, sml_id AS LayerID, UNIX_TIMESTAMP(sml_created_at) as CreatedTime, UNIX_TIMESTAMP(sml_modified_at) as ModifiedTime, sml_subuser_creator as CreatorID, sml_subuser_updater as UpdaterID, IF( sml_subuser_creator = 0, sml_vendor_id, IF( sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) ) ) as CreatorName, IF( sml_subuser_updater = 0, sml_vendor_id, IF( sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) ) ) as UpdaterName, smlsub_created AS DateTime, 1 AS AutoSave, sitefotos_map_autolayers.smlsub_auto_id AS AutoID, sml_building_id AS BuildingID, mb_nickname as BuildingName FROM sitefotos_map_layers LEFT outer JOIN sitefotos_map_autolayers on sml_id = smlsub_layer_id left outer join maptile_building on sitefotos_map_layers.sml_building_id = mb_id WHERE sml_vendor_id = ? AND sml_active = '1' AND smlsub_layer_id IS NOT NULL ${restrictedUser && userContactId>0 ? `AND (mb_manager = ${userContactId} OR mb_manager_secondary =${userContactId} OR mb_manager_third =${userContactId})` : ''}`;

      maps = await mysql.awaitQuery(query, [vendorId, vendorId]);
      await redisClient.set(`GET_MAPS_TABULATOR:${id}`, JSON.stringify(maps), {EX: 60 * 60});
    }

    let uniqueUpdators = [...maps.reduce((acc, e) => e.UpdaterName && !acc.get(e.UpdaterName) ? acc.set(e.UpdaterName, true) : acc, new Map()).keys()].sort((a, b) => a.localeCompare(b));

    let uniqueCreators = [...maps.reduce((acc, e) => e.CreatorName && !acc.get(e.CreatorName) ? acc.set(e.CreatorName, true) : acc, new Map()).keys()].sort((a, b) => a.localeCompare(b));


    const total = maps.length;




    if (filter.length > 0) {
      for (let f of filter) {
        //if f.value is empty array or empty value then skip
        if (!f.value || (Array.isArray(f.value) && f.value.length == 0)) continue;
        switch (f.field) {


          case 'LayerName':
            maps = maps.filter(item => item.LayerName ? item.LayerName.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
            break;
          case 'BuildingName':
            maps = maps.filter(item => item.BuildingName ? item.BuildingName.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
            break;
          case 'Address':
            maps = maps.filter(item => item.Address ? item.Address.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
            break;
          case 'City':
            maps = maps.filter(item => item.City ? item.City.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
            break;
          case 'State':
            maps = maps.filter(item => item.State ? item.State == f.value : false);
            break;
          case 'UpdaterName':
            maps = maps.filter(item => item.UpdaterName ? item.UpdaterName.toString().toLowerCase().includes(f.value.toString().toLowerCase()) : false);
            break;
          case 'Zip':
            maps = maps.filter(item => item.Zip ? item.Zip == f.value : false);
            break;
          case 'CreatorName':
            maps = maps.filter(item => item.CreatorName ? item.CreatorName.toString().includes(f.value.toString()) : false);
            break;
          case 'CreatedTime':
            if (f.value.start && f.value.end) {
              let start = new Date(f.value.start).getTime() / 1000;
              let end = new Date(f.value.end).getTime() / 1000;
              maps = maps.filter(item => item.CreatedTime ? (item.CreatedTime >= start && item.CreatedTime <= end) : false)
            }
            break;
          case 'ModifiedTime':
            if (f.value.start && f.value.end) {
              let start = new Date(f.value.start).getTime() / 1000;
              let end = new Date(f.value.end).getTime() / 1000;
              maps = maps.filter(item => item.ModifiedTime ? (item.ModifiedTime >= start && item.ModifiedTime <= end) : false)
            }

            break;

        }
      }
    }



    if (sort.length > 0) {
      maps.sort((a, b) => {
        for (let s of sort) {
          if (a[s.field] > b[s.field]) {
            return s.dir.toUpperCase() === 'ASC' ? 1 : -1;
          } else if (a[s.field] < b[s.field]) {
            return s.dir.toUpperCase() === 'ASC' ? -1 : 1;
          }
        }
        return 0;
      });
    }


    const filteredSortedTotal = maps.length;


    const requestedSize = req.body.size;
    const page = req.body.page || 1;
    const defaultSize = 250;





    const showAll = requestedSize === true || requestedSize === 'true' ||
                   requestedSize === false || requestedSize === 'false' ||
                   requestedSize === null || requestedSize === undefined ||
                   requestedSize <= 0 || requestedSize === -1;

    let finalSize = showAll ? filteredSortedTotal : (requestedSize || defaultSize);

    if (!showAll && finalSize <= 0) {
        finalSize = defaultSize;
    }

    if (finalSize === 0) finalSize = defaultSize;


    const paginatedData = showAll ? maps : maps.slice((page - 1) * finalSize, page * finalSize);



    res.json({
      data: paginatedData,
      last_page: showAll ? 1 : Math.ceil(filteredSortedTotal / finalSize),
      last_row: filteredSortedTotal,
      creators: uniqueCreators,
      updators: uniqueUpdators,
      count: filteredSortedTotal,
      total: total
    });

  } catch (ex) {
    next(ex);
  }
}


const getMapsForSite = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    const id = req.params.id
    const maps = await mysql.awaitSafeQuery(`SELECT sml_id, sml_layer_name, sml_urlkey from sitefotos_map_layers where sml_building_id = ? and sml_vendor_id=?`, [id, vendorId]);
    res.json(maps);
  } catch (ex) {
    next(ex)
  }
}

const getMapsForMapListComponent = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    const id = req.params.id
    const maps = await mysql.awaitSafeQuery(`
                SELECT *,
                IF( sml_subuser_creator = 0, CONCAT(vendor_fname, ' ', vendor_lname), IF( sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) ) ) as CreatorName
                FROM sitefotos_map_layers sml
               left outer join maptile_vendors on sml.sml_vendor_id = maptile_vendors.vendor_id
                where sml_building_id = ? and sml_vendor_id=? and sml_active = '1' `,
      [id, vendorId]);
    res.json(maps);
  } catch (ex) {
    next(ex)
  }
}

const getSavedMapLayersTabulator = async (req, res, next) => {
  try {
    const accessCode = req.body.accessCode || req.query.accessCode;
    const token = req.cookies.JWT;
    const { vendorId } = await login(token, accessCode);
    if (!vendorId) {
      return res.sendStatus(401);
    }


    // Get sites
    const sites = await mysql.awaitSafeQuery(
      "SELECT mb_id, mb_nickname, mb_address1, mb_zip_code, mb_geo, mb_lat, mb_long, (SELECT City FROM maptile_city WHERE CityId = mb_city) AS cityname, (SELECT state_abv_name FROM maptile_state WHERE id = mb_state) AS statename FROM maptile_building WHERE mb_user_id = ? AND mb_user_type = '1' AND mb_status = '1' ORDER BY mb_nickname",
      [vendorId]
    );

    const query = /*SQL*/ `SELECT CONCAT(vendor_fname, ' ', vendor_lname) as Vendor, sml_address1 as Address, sml_city as City, sml_state as State, sml_zip_code as Zip, sml_layer_name AS LayerName, sml_urlkey, sml_id AS LayerID, sml_created_at as CreatedTime, sml_modified_at as ModifiedTime, sml_subuser_creator as CreatorID, sml_subuser_updater as UpdaterID, IF( sml_subuser_creator = 0, CONCAT(vendor_fname, ' ', vendor_lname), IF( sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) ) ) as CreatorName, IF( sml_subuser_updater = 0, CONCAT(vendor_fname, ' ', vendor_lname), IF( sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) ) ) as UpdaterName, sml_created AS DateTime, 0 AS AutoSave, NULL as AutoID, sml_building_id AS BuildingID, mb_nickname as BuildingName FROM sitefotos_map_layers left outer join maptile_vendors on sitefotos_map_layers.sml_vendor_id = maptile_vendors.vendor_id left outer join maptile_building on sitefotos_map_layers.sml_building_id = mb_id WHERE sml_vendor_id = ? AND sml_active = '1' UNION SELECT sml_vendor_id, sml_address1 as Address, sml_city as City, sml_state as State, sml_zip_code as Zip, sml_layer_name AS LayerName, sml_urlkey, sml_id AS LayerID, sml_created_at as CreatedTime, sml_modified_at as ModifiedTime, sml_subuser_creator as CreatorID, sml_subuser_updater as UpdaterID, IF( sml_subuser_creator = 0, sml_vendor_id, IF( sml_subuser_creator = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_creator ) ) ) as CreatorName, IF( sml_subuser_updater = 0, sml_vendor_id, IF( sml_subuser_updater = -1, 'Property Takeoffs', ( select CONCAT( sf_contact_fname, ' ', sf_contact_lname ) from sitefotos_user_permissions_users left join sitefotos_contacts on supu_contact_id = sf_contact_id where supu_id = sml_subuser_updater ) ) ) as UpdaterName, smlsub_created AS DateTime, 1 AS AutoSave, sitefotos_map_autolayers.smlsub_auto_id AS AutoID, sml_building_id AS BuildingID, mb_nickname as BuildingName FROM sitefotos_map_layers LEFT outer JOIN sitefotos_map_autolayers on sml_id = smlsub_layer_id left outer join maptile_building on sitefotos_map_layers.sml_building_id = mb_id WHERE sml_vendor_id = ? AND sml_active = '1' AND smlsub_layer_id IS NOT NULL`;
    // Apply filters
    const filters = req.body.filters || {};

    const filterQuery = Object.entries(filters)
      .map(([key, value]) => {
        if (value && value.length > 0) {
          return `(${key} LIKE '%${value}%')`;
        }
        return null;
      })
      .filter(Boolean)
      .join(" AND ");

    const filteredQuery = filterQuery ? `${query} AND ${filterQuery}` : query;

    const totalQuery = `SELECT COUNT(*) as total FROM (${filteredQuery}) as filtered`;

    const totalResult = await mysql.awaitSafeQuery(totalQuery, [vendorId, vendorId]);
    const total = totalResult[0].total;

// Apply sorting
    const { sortBy, sortDesc } = req.body.options || {};
    const sortField = sortBy && sortBy[0];
    const sortDirection = sortDesc && sortDesc[0] ? "DESC" : "ASC";

    const sortedQuery = sortField
      ? `${filteredQuery} ORDER BY ${sortField} ${sortDirection}`
      : filteredQuery;

// Apply pagination
    const { page, size } = req.query || {};
    const start = (page - 1) * size;
    const paginatedQuery = `${sortedQuery} LIMIT ${start}, ${size}`;

    const maps = await mysql.awaitSafeQuery(paginatedQuery, [vendorId, vendorId]);

    res.status(200).send({
      data: maps,
      count: total,
      last_page: Math.ceil(total/ size),
      last_row: total,
      sites: sites
    });
  } catch (error) {
    console.error(error);
    res.sendStatus(500);
  }
};

const mapMeasurements = async function (req, res, next) {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    const orders = req.body.maps;
    let results = await maps.getSfMapLayers(orders.join(','));
    let csvRows = await maps.getCsvMapMeasurements(results);
    const headers = csvRows[0];
    const rows = csvRows.slice(1);
    const convertedToKeyValueForGoogleSheet = rows.map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = row[index];
      });
      return obj;
    });
    let sheetName = `Maps_Export_${moment().unix()}`;
    let sheetUrl = await maps.postToGoogle(
      'https://script.google.com/macros/s/AKfycbwRCAg5YJmwvFdAOwlDNPZRv8lXHN5iI46qzRvkMALn-A7mWRnigZPHAPbgS3si9-kLIQ/exec',
      vendorId,
      sheetName,
      convertedToKeyValueForGoogleSheet
    );

    res.status(200).send(sheetUrl);
  } catch (error) {
    console.log("Error in map measurements" + error);
    next(error);
  }
};


module.exports = {
  test,
  mapbuilderLink,
  createSiteMap,
  getAppleToken,
  getMapLayerData,
  transferMaps,
  transferMapsPto,
  transferMapsPtoInternalSitefotosMaps,
  getAspireSites,
  getAspireTakeoffs,
  pushAspireTakeoffs,
  getDefaultAssociations,
  saveMap,
  backgroundUpdateMeasurements,
  saveMapbyAccessCode,
  backgroundUpdateScreenshots,
  backgroundUpdateBounds,
  convertSiteRecon,
  convertSiteRecon2,
  copyMaps,
  mapbuilderPT,
  copySingleMap,
  convertAttentive,
  convertGoilawn,
  openMapLayer,
  openMapLayerNew,
  getSavedMapLayers,
  exportMapAsGoogleSheet,
  getSavedMapLayersTabulator,
  mapMeasurements,
  getSavedMapLayersTabulator2,
  plotPhotoMap,
  convertAttentiveSelective,
  getMapsTabulator,
  getMapsForSite,
  getMapsForMapListComponent,
  mapMeasurementsGoogleSheets,
  mapMeasurementsGoogleSheetsPdf
}
