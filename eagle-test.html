<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>EagleView Test</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">


  <link
    rel="stylesheet"
    href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
  />

  <style>
    html, body, #map {
      margin: 0;
      padding: 0;
      height: 100%;
      width: 100%;
    }


    #searchBox {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1000;
      background: rgba(255,255,255,0.9);
      padding: 6px;
      border-radius: 4px;
      box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    }
    #addressInput {
      width: 240px;
      padding: 4px 6px;
      font-size: 14px;
      border: 1px solid #ccc;
      border-radius: 2px;
    }


    .pac-container {
      z-index: 10000 !important;
    }
  </style>
</head>
<body>


  <div id="searchBox">
    <input
      id="addressInput"
      type="text"
      placeholder="Search address..."
      autocomplete="off"
    />
  </div>

  
  <div id="map"></div>

 
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" defer></script>

  <script>
   
    const apiKey        = '8329d048-70f6-4bf3-8619-62ef642970cd';
    const layer         = 'Latest';
    const styleId       = 'default';
    const tileMatrixSet = 'GoogleMapsCompatible_9-23';
    const format        = 'png';
    const wmtsUrl = [
      'https://apis.eagleview.com/imagery/wmts/v1/visual/tile',
      layer,
      styleId,
      tileMatrixSet,
      '{z}/{x}/{y}.' + format
    ].join('/') + '?api_key=' + apiKey;
    

    let map;

    function initAutocomplete() {
  
  map = L.map('map').setView([40.730610, -73.935242], 10);

  L.tileLayer(wmtsUrl, {
    tileSize:    256,
    maxZoom:     23,
    attribution: '&copy; EagleView',
    tms:         false
  }).addTo(map);

  const input = document.getElementById('addressInput');
  const autocomplete = new google.maps.places.Autocomplete(input, {
    types: ['address']  
  });

  autocomplete.addListener('place_changed', () => {
    const place = autocomplete.getPlace();
    if (!place.geometry) {
      alert('No details available for that address.');
      return;
    }

    
    if (place.geometry.viewport) {
      const sw = place.geometry.viewport.getSouthWest();
      const ne = place.geometry.viewport.getNorthEast();
      map.fitBounds([
        [sw.lat(), sw.lng()],
        [ne.lat(), ne.lng()]
      ]);
    } else {
      map.setView(
        [place.geometry.location.lat(), place.geometry.location.lng()],
        16
      );
    }
  });
}
  </script>

 
  <script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCHffyE3zbfCDeezpIT1pt8-mdgwYCmYvY&libraries=places&callback=initAutocomplete"
    async
    defer
  ></script>
</body>
</html>
