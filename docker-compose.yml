version: "3"

services:
  npm-installer:
    image: sitefotos/sitefotos-node:20.19.4
    command: [ "/bin/sh", "-c", "cd /node-server && npm install -q" ]
    volumes:
      - ./node-server:/node-server

  web:
    build: ./ci
    hostname: http
    restart: always
    ports:
      - "80:80"
      - "${MY_DOCKER_IP:-127.0.0.1}:9100:9000"
    volumes:
      - ./:/builds/sitefotos-backend/sitefotos
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      DATABASE_PARAMS_HOST: ${DATABASE_PARAMS_HOST-db}
      DATABASE_PARAMS_PASSWORD: ${DATABASE_PARAMS_PASSWORD}
      DATABASE_PARAMS_USERNAME: ${DATABASE_PARAMS_USERNAME-root}
      DATABASE_PARAMS_DBNAME: ${DATABASE_PARAMS_DBNAME-sitefotos}
      DATABASE_PARAMS_PORT: ${DATABASE_PARAMS_PORT-3306}
      GOTENBERG_SERVICE_HOST: ${GOTENBERG_SERVICE_HOST-gotenberg}
      REDIS_SERVICE_HOST: redis
      REDIS_SERVICE_PORT: 6379
      RABBITMQ_DEFAULT_USER: "guest"
      BASE_URL: ${BASE_URL-http://127.0.0.1}
      RABBITMQ_DEFAULT_PASS: "guest"
      RABBITMQ_SERVICE_HOST: "rabbitmq"
      RABBITMQ_SERVICE_PORT: 5672
      TWILIO_SID: ${TWILIO_SID}
      TWILIO_TOKEN: ${TWILIO_TOKEN}
      RACKSPACE_KEY: ${RACKSPACE_KEY-39639562d3c9654ce1fcd87381206cca}
      CHARGIFY_SUB_DOMAIN: ${CHARGIFY_SUB_DOMAIN-sitefotosdev}
      AWS_ACCESS_KEY: ${AWS_ACCESS_KEY}
      AWS_SECRET: ${AWS_SECRET}
      CHARGIFY_API_KEY: ${CHARGIFY_API_KEY-QSpZ6OAuPSwdWjqEi9iG1rBK3soOdgHPG1AVFiiQvq8} # test key, does not charge
      MANDRILL_KEY: ${MANDRILL_KEY-**********************} # test key, does not email
    links:
      - db
      - redis
      - rabbitmq
      - nodejs
      - mobile
      - appdata
      - apiv1
    depends_on:
      - db
      - redis
      - rabbitmq
      - nodejs

  nodejs:
    image: sitefotos/sitefotos-node:20.19.4
    restart: always
    ports:
      - "${MY_DOCKER_IP:-127.0.0.1}:3000:3000"
      - "${MY_DOCKER_IP:-127.0.0.1}:9229:9229"
    volumes:
      - ./node-server:/node-server
      - ./wait-for-it.sh:/wait-for-it.sh
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      DATABASE_PARAMS_HOST: ${DATABASE_PARAMS_HOST-db}
      DATABASE_PARAMS_PASSWORD: ${DATABASE_PARAMS_PASSWORD}
      DATABASE_PARAMS_USERNAME: ${DATABASE_PARAMS_USERNAME-root}
      DATABASE_PARAMS_DBNAME: ${DATABASE_PARAMS_DBNAME-sitefotos}
      DATABASE_PARAMS_PORT: ${DATABASE_PARAMS_PORT-3306}
      POSTGRES_PARAMS_HOST: ${POSTGRES_PARAMS_HOST-postgres}
      FLEETDB_PARAMS_PASSWORD: ${FLEETDB_PARAMS_PASSWORD-password}
      FLEETDB_PARAMS_USERNAME: ${FLEETDB_PARAMS_USERNAME-user}
      FLEETDB_PARAMS_DBNAME: ${FLEETDB_PARAMS_DBNAME-sitefotos_db_fleets}
      POSTGRES_PARAMS_PORT: ${POSTGRES_PARAMS_PORT-5432}
      GOTENBERG_SERVICE_HOST: ${GOTENBERG_SERVICE_HOST-gotenberg}
      BASE_URL: ${BASE_URL-http://127.0.0.1}
      WORKORDERS_S3_ENDPOINT: ${WORKORDERS_S3_ENDPOINT}
      UPSTASH_REDIS_CONNECTION_STRING: ${UPSTASH_REDIS_CONNECTION_STRING-rediss://default:<EMAIL>:41775}
      WORKORDERS_S3_REGION: ${WORKORDERS_S3_REGION-us-east-1}
      WORKORDERS_S3_ACCESS_KEY: ${WORKORDERS_S3_ACCESS_KEY}
      WORKORDERS_S3_SECRET_KEY: ${WORKORDERS_S3_SECRET_KEY}
      FILES_UPLOAD_ENDPOINT: ${FILES_UPLOAD_ENDPOINT}
      FILES_S3_REGION: ${FILES_S3_REGION-us-east-1}
      FILES_S3_ACCESS_KEY: ${FILES_S3_ACCESS_KEY}
      FILES_S3_SECRET_KEY: ${FILES_S3_SECRET_KEY}
      APPLE_PRIVATE_KEY: ${APPLE_PRIVATE_KEY}
      CHARGIFY_SUB_DOMAIN: ${CHARGIFY_SUB_DOMAIN-sitefotosdev}
      MANDRILL_KEY: ${MANDRILL_KEY-**********************} # test key, does not email
      CHARGIFY_API_KEY: ${CHARGIFY_API_KEY-QSpZ6OAuPSwdWjqEi9iG1rBK3soOdgHPG1AVFiiQvq8} # test key, does not charge
      QUICKBOOKS_CLIENT_ID: ${QUICKBOOKS_CLIENT_ID-ABfP4Mr9CSaCiG7j2h4vyglzJgKMEfRezPOImj0wVpmPcge0g5} #sandbox
      QUICKBOOKS_CLIENT_SECRET: ${QUICKBOOKS_CLIENT_SECRET-U2RaREeF8EwADHFeX6FDjDvDSg4H4JKaMiapoqQp} #sandbox
      PHOTOUPLOAD_SIGN_KEY: ${PHOTOUPLOAD_SIGN_KEY}
      AWS_ACCESS_KEY: ${AWS_ACCESS_KEY}
      AWS_SECRET: ${AWS_SECRET}
      APNS_KEY_ID: ${APNS_KEY_ID}
      APNS_TEAM_ID: ${APNS_TEAM_ID}
      APNS_BUNDLE_ID: ${APNS_BUNDLE_ID}
      APNS_AUTH_KEY: ${APNS_AUTH_KEY}
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_PRIVATE_KEY_ID: ${FIREBASE_PRIVATE_KEY_ID}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}
      FIREBASE_CLIENT_ID: ${FIREBASE_CLIENT_ID}
      FIREBASE_CLIENT_X509_CERT_URL: ${FIREBASE_CLIENT_X509_CERT_URL}
      K8S_BRANCH: ${K8S_BRANCH-local}
      DEEPINFRA_API_KEY: ${DEEPINFRA_API_KEY}
      OPENROUTER_API_KEY: ${OPENROUTER_API_KEY}
      PINECONE_API_KEY: ${PINECONE_API_KEY}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      GOOGLE_APPLICATION_CREDENTIALS: ${GOOGLE_APPLICATION_CREDENTIALS}
      REDIS_PERSISTENT_SERVICE_HOST: ${REDIS_PERSISTENT_SERVICE_HOST}
      REDIS_PERSISTENT_SERVICE_PORT: ${REDIS_PERSISTENT_SERVICE_PORT}
      REDIS_PERSISTENT_USERNAME: ${REDIS_PERSISTENT_USERNAME}
      REDIS_PERSISTENT_PASSWORD: ${REDIS_PERSISTENT_PASSWORD}
    command: [ "/bin/sh", "-c", "chmod +x /wait-for-it.sh && /wait-for-it.sh redis:6379 -t 60 -- sh -c 'cd /node-server && npm run watch && echo HERE'" ]
    links:
      - postgres
      - db
      - redis
      - gotenberg
    depends_on:
      - postgres
      - db
      - redis
      - gotenberg
      - npm-installer

  mobile:
    image: sitefotos/sitefotos-node:20.19.4
    restart: always
    ports:
      - "${MY_DOCKER_IP:-127.0.0.1}:3001:3001"
    volumes:
      - ./node-server:/node-server
      - ./wait-for-it.sh:/wait-for-it.sh
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      DATABASE_PARAMS_HOST: ${DATABASE_PARAMS_HOST-db}
      DATABASE_PARAMS_PASSWORD: ${DATABASE_PARAMS_PASSWORD}
      DATABASE_PARAMS_USERNAME: ${DATABASE_PARAMS_USERNAME-root}
      DATABASE_PARAMS_DBNAME: ${DATABASE_PARAMS_DBNAME-sitefotos}
      DATABASE_PARAMS_PORT: ${DATABASE_PARAMS_PORT-3306}
      POSTGRES_PARAMS_HOST: ${POSTGRES_PARAMS_HOST-postgres}
      FLEETDB_PARAMS_PASSWORD: ${FLEETDB_PARAMS_PASSWORD-password}
      FLEETDB_PARAMS_USERNAME: ${FLEETDB_PARAMS_USERNAME-user}
      FLEETDB_PARAMS_DBNAME: ${FLEETDB_PARAMS_DBNAME-sitefotos_db_fleets}
      POSTGRES_PARAMS_PORT: ${POSTGRES_PARAMS_PORT-5432}
      GOTENBERG_SERVICE_HOST: ${GOTENBERG_SERVICE_HOST-gotenberg}
      BASE_URL: ${BASE_URL-http://127.0.0.1}
      UPSTASH_REDIS_CONNECTION_STRING: ${UPSTASH_REDIS_CONNECTION_STRING-rediss://default:<EMAIL>:41775}
      WORKORDERS_S3_ENDPOINT: ${WORKORDERS_S3_ENDPOINT}
      WORKORDERS_S3_REGION: ${WORKORDERS_S3_REGION-us-east-1}
      WORKORDERS_S3_ACCESS_KEY: ${WORKORDERS_S3_ACCESS_KEY}
      WORKORDERS_S3_SECRET_KEY: ${WORKORDERS_S3_SECRET_KEY}
      APPLE_PRIVATE_KEY: ${APPLE_PRIVATE_KEY}
      CHARGIFY_SUB_DOMAIN: ${CHARGIFY_SUB_DOMAIN-sitefotosdev}
      MANDRILL_KEY: ${MANDRILL_KEY-**********************} # test key, does not email
      CHARGIFY_API_KEY: ${CHARGIFY_API_KEY-QSpZ6OAuPSwdWjqEi9iG1rBK3soOdgHPG1AVFiiQvq8} # test key, does not charge
      QUICKBOOKS_CLIENT_ID: ${QUICKBOOKS_CLIENT_ID-ABfP4Mr9CSaCiG7j2h4vyglzJgKMEfRezPOImj0wVpmPcge0g5} #sandbox
      QUICKBOOKS_CLIENT_SECRET: ${QUICKBOOKS_CLIENT_SECRET-U2RaREeF8EwADHFeX6FDjDvDSg4H4JKaMiapoqQp} #sandbox
      PHOTOUPLOAD_SIGN_KEY: ${PHOTOUPLOAD_SIGN_KEY}
      AWS_ACCESS_KEY: ${AWS_ACCESS_KEY}
      AWS_SECRET: ${AWS_SECRET}
      APNS_KEY_ID: ${APNS_KEY_ID}
      APNS_TEAM_ID: ${APNS_TEAM_ID}
      APNS_BUNDLE_ID: ${APNS_BUNDLE_ID}
      APNS_AUTH_KEY: ${APNS_AUTH_KEY}
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_PRIVATE_KEY_ID: ${FIREBASE_PRIVATE_KEY_ID}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}
      FIREBASE_CLIENT_ID: ${FIREBASE_CLIENT_ID}
      FIREBASE_CLIENT_X509_CERT_URL: ${FIREBASE_CLIENT_X509_CERT_URL}
      K8S_BRANCH: ${K8S_BRANCH-local}
    command: [ "/bin/sh", "-c", "chmod +x /wait-for-it.sh && /wait-for-it.sh redis:6379 -t 60 -- sh -c 'cd /node-server && npm run watch-mobile && echo HERE'" ]
    links:
      - postgres
      - db
      - redis
      - gotenberg
    depends_on:
      - postgres
      - db
      - redis
      - gotenberg
      - npm-installer

  appdata:
    image: sitefotos/sitefotos-node:20.19.4
    restart: always
    ports:
      - "${MY_DOCKER_IP:-127.0.0.1}:3004:3004"
      - "${MY_DOCKER_IP:-127.0.0.1}:9231:9231"
    volumes:
      - ./node-server:/node-server
      - ./wait-for-it.sh:/wait-for-it.sh
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      DATABASE_PARAMS_HOST: ${DATABASE_PARAMS_HOST-db}
      DATABASE_PARAMS_PASSWORD: ${DATABASE_PARAMS_PASSWORD}
      DATABASE_PARAMS_USERNAME: ${DATABASE_PARAMS_USERNAME-root}
      DATABASE_PARAMS_DBNAME: ${DATABASE_PARAMS_DBNAME-sitefotos}
      DATABASE_PARAMS_PORT: ${DATABASE_PARAMS_PORT-3306}
      POSTGRES_PARAMS_HOST: ${POSTGRES_PARAMS_HOST-postgres}
      FLEETDB_PARAMS_PASSWORD: ${FLEETDB_PARAMS_PASSWORD-password}
      FLEETDB_PARAMS_USERNAME: ${FLEETDB_PARAMS_USERNAME-user}
      FLEETDB_PARAMS_DBNAME: ${FLEETDB_PARAMS_DBNAME-sitefotos_db_fleets}
      POSTGRES_PARAMS_PORT: ${POSTGRES_PARAMS_PORT-5432}
      GOTENBERG_SERVICE_HOST: ${GOTENBERG_SERVICE_HOST-gotenberg}
      BASE_URL: ${BASE_URL-http://127.0.0.1}
      UPSTASH_REDIS_CONNECTION_STRING: ${UPSTASH_REDIS_CONNECTION_STRING-rediss://default:<EMAIL>:41775}
      WORKORDERS_S3_ENDPOINT: ${WORKORDERS_S3_ENDPOINT}
      WORKORDERS_S3_REGION: ${WORKORDERS_S3_REGION-us-east-1}
      WORKORDERS_S3_ACCESS_KEY: ${WORKORDERS_S3_ACCESS_KEY}
      WORKORDERS_S3_SECRET_KEY: ${WORKORDERS_S3_SECRET_KEY}
      APPLE_PRIVATE_KEY: ${APPLE_PRIVATE_KEY}
      CHARGIFY_SUB_DOMAIN: ${CHARGIFY_SUB_DOMAIN-sitefotosdev}
      MANDRILL_KEY: ${MANDRILL_KEY-**********************} # test key, does not email
      CHARGIFY_API_KEY: ${CHARGIFY_API_KEY-QSpZ6OAuPSwdWjqEi9iG1rBK3soOdgHPG1AVFiiQvq8} # test key, does not charge
      QUICKBOOKS_CLIENT_ID: ${QUICKBOOKS_CLIENT_ID-ABfP4Mr9CSaCiG7j2h4vyglzJgKMEfRezPOImj0wVpmPcge0g5} #sandbox
      QUICKBOOKS_CLIENT_SECRET: ${QUICKBOOKS_CLIENT_SECRET-U2RaREeF8EwADHFeX6FDjDvDSg4H4JKaMiapoqQp} #sandbox
      PHOTOUPLOAD_SIGN_KEY: ${PHOTOUPLOAD_SIGN_KEY}
      AWS_ACCESS_KEY: ${AWS_ACCESS_KEY}
      AWS_SECRET: ${AWS_SECRET}
      APNS_KEY_ID: ${APNS_KEY_ID}
      APNS_TEAM_ID: ${APNS_TEAM_ID}
      APNS_BUNDLE_ID: ${APNS_BUNDLE_ID}
      APNS_AUTH_KEY: ${APNS_AUTH_KEY}
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_PRIVATE_KEY_ID: ${FIREBASE_PRIVATE_KEY_ID}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}
      FIREBASE_CLIENT_ID: ${FIREBASE_CLIENT_ID}
      FIREBASE_CLIENT_X509_CERT_URL: ${FIREBASE_CLIENT_X509_CERT_URL}
      K8S_BRANCH: ${K8S_BRANCH-local}
    command: [ "/bin/sh", "-c", "chmod +x /wait-for-it.sh && /wait-for-it.sh redis:6379 -t 60 -- sh -c 'cd /node-server && npm run watch-app-data && echo HERE'" ]
    links:
      - postgres
      - db
      - redis
      - gotenberg
    depends_on:
      - postgres
      - db
      - redis
      - gotenberg
      - npm-installer

  apiv1:
    image: sitefotos/sitefotos-node:20.19.4
    restart: always
    ports:
      - "${MY_DOCKER_IP:-127.0.0.1}:3002:3002"
    volumes:
      - ./node-server:/node-server
      - ./wait-for-it.sh:/wait-for-it.sh
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      DATABASE_PARAMS_HOST: ${DATABASE_PARAMS_HOST-db}
      DATABASE_PARAMS_PASSWORD: ${DATABASE_PARAMS_PASSWORD}
      DATABASE_PARAMS_USERNAME: ${DATABASE_PARAMS_USERNAME-root}
      DATABASE_PARAMS_DBNAME: ${DATABASE_PARAMS_DBNAME-sitefotos}
      DATABASE_PARAMS_PORT: ${DATABASE_PARAMS_PORT-3306}
      POSTGRES_PARAMS_HOST: ${POSTGRES_PARAMS_HOST-postgres}
      FLEETDB_PARAMS_PASSWORD: ${FLEETDB_PARAMS_PASSWORD-password}
      FLEETDB_PARAMS_USERNAME: ${FLEETDB_PARAMS_USERNAME-user}
      FLEETDB_PARAMS_DBNAME: ${FLEETDB_PARAMS_DBNAME-sitefotos_db_fleets}
      POSTGRES_PARAMS_PORT: ${POSTGRES_PARAMS_PORT-5432}
      GOTENBERG_SERVICE_HOST: ${GOTENBERG_SERVICE_HOST-gotenberg}
      BASE_URL: ${BASE_URL-http://127.0.0.1}
      UPSTASH_REDIS_CONNECTION_STRING: ${UPSTASH_REDIS_CONNECTION_STRING-rediss://default:<EMAIL>:41775}
      WORKORDERS_S3_ENDPOINT: ${WORKORDERS_S3_ENDPOINT}
      WORKORDERS_S3_REGION: ${WORKORDERS_S3_REGION-us-east-1}
      WORKORDERS_S3_ACCESS_KEY: ${WORKORDERS_S3_ACCESS_KEY}
      WORKORDERS_S3_SECRET_KEY: ${WORKORDERS_S3_SECRET_KEY}
      APPLE_PRIVATE_KEY: ${APPLE_PRIVATE_KEY}
      CHARGIFY_SUB_DOMAIN: ${CHARGIFY_SUB_DOMAIN-sitefotosdev}
      MANDRILL_KEY: ${MANDRILL_KEY-**********************} # test key, does not email
      CHARGIFY_API_KEY: ${CHARGIFY_API_KEY-QSpZ6OAuPSwdWjqEi9iG1rBK3soOdgHPG1AVFiiQvq8} # test key, does not charge
      QUICKBOOKS_CLIENT_ID: ${QUICKBOOKS_CLIENT_ID-ABfP4Mr9CSaCiG7j2h4vyglzJgKMEfRezPOImj0wVpmPcge0g5} #sandbox
      QUICKBOOKS_CLIENT_SECRET: ${QUICKBOOKS_CLIENT_SECRET-U2RaREeF8EwADHFeX6FDjDvDSg4H4JKaMiapoqQp} #sandbox
      PHOTOUPLOAD_SIGN_KEY: ${PHOTOUPLOAD_SIGN_KEY}
      AWS_ACCESS_KEY: ${AWS_ACCESS_KEY}
      AWS_SECRET: ${AWS_SECRET}
      APNS_KEY_ID: ${APNS_KEY_ID}
      APNS_TEAM_ID: ${APNS_TEAM_ID}
      APNS_BUNDLE_ID: ${APNS_BUNDLE_ID}
      APNS_AUTH_KEY: ${APNS_AUTH_KEY}
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID}
      FIREBASE_PRIVATE_KEY_ID: ${FIREBASE_PRIVATE_KEY_ID}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL}
      FIREBASE_CLIENT_ID: ${FIREBASE_CLIENT_ID}
      FIREBASE_CLIENT_X509_CERT_URL: ${FIREBASE_CLIENT_X509_CERT_URL}
      K8S_BRANCH: ${K8S_BRANCH-local}
    command: [ "/bin/sh", "-c", "chmod +x /wait-for-it.sh && /wait-for-it.sh redis:6379 -t 60 -- sh -c 'cd /node-server && npm run watch-api && echo HERE'" ]
    links:
      - postgres
      - db
      - redis
      - gotenberg
    depends_on:
      - postgres
      - db
      - redis
      - gotenberg
      - npm-installer


  nodejs-worker:
    image: sitefotos/sitefotos-node:20.19.4 
    restart: always
    volumes:
      - ./node-server:/node-server
      - ./wait-for-it.sh:/wait-for-it.sh 
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      NODE_ENV: ${NODE_ENV-development}
      K8S_BRANCH: ${K8S_BRANCH-local} 
      DATABASE_PARAMS_HOST: ${DATABASE_PARAMS_HOST-db}
      DATABASE_PARAMS_PASSWORD: ${DATABASE_PARAMS_PASSWORD}
      DATABASE_PARAMS_USERNAME: ${DATABASE_PARAMS_USERNAME-root}
      DATABASE_PARAMS_DBNAME: ${DATABASE_PARAMS_DBNAME-sitefotos}
      DATABASE_PARAMS_PORT: ${DATABASE_PARAMS_PORT-3306}
      POSTGRES_PARAMS_HOST: ${POSTGRES_PARAMS_HOST-postgres}
      FLEETDB_PARAMS_PASSWORD: ${FLEETDB_PARAMS_PASSWORD-password}
      FLEETDB_PARAMS_USERNAME: ${FLEETDB_PARAMS_USERNAME-user}
      FLEETDB_PARAMS_DBNAME: ${FLEETDB_PARAMS_DBNAME-sitefotos_db_fleets}
      POSTGRES_PARAMS_PORT: ${POSTGRES_PARAMS_PORT-5432}
      REDIS_SERVICE_HOST: redis 
      REDIS_SERVICE_PORT: 6379
      MANDRILL_KEY: ${MANDRILL_KEY-**********************}
      AWS_ACCESS_KEY: ${AWS_ACCESS_KEY}
      AWS_SECRET: ${AWS_SECRET}
    command: [ "/bin/sh", "-c", "chmod +x /wait-for-it.sh && /wait-for-it.sh redis:6379 -t 60 -- sh -c 'cd /node-server && node worker.js'" ]
    links:
      - postgres
      - db
      - redis
    depends_on:
      - postgres
      - db
      - redis
      - npm-installer 

  db:
    image: mysql:8.0.31
    command: mysqld --sql_mode=""
    restart: always
    environment: # https://dev.mysql.com/doc/refman/5.7/en/docker-mysql-more-topics.html
      MYSQL_DATABASE: "sitefotos"
      MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
      MYSQL_ROOT_PASSWORD: ""
      MYSQL_ROOT_HOST: "%"
    ports:
      - "${MY_DOCKER_IP:-127.0.0.1}:3306:3306"

  postgres:
    image: postgis/postgis:12-3.0
    restart: always
    environment:
      POSTGRES_DB: sitefotos_db_fleets
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
       - ./ci/postgres-init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "${MY_DOCKER_IP:-127.0.0.1}:5432:5432"

  gotenberg:
    image: gotenberg/gotenberg:8.21.1
    command:
      - "gotenberg"
      - "--api-port=80"

  redis:
    image: redis:6.2.11
    restart: always
    ports:
      - "${MY_DOCKER_IP:-127.0.0.1}:6379:6379"

  rabbitmq:
    image: library/rabbitmq:3.8.19-management-alpine
    container_name: 'rabbitmq'
    environment: #https://hub.docker.com/_/rabbitmq
      # These is the default user/pass. Management tool is at http://localhost:15672/
      RABBITMQ_DEFAULT_USER: "guest"
      RABBITMQ_DEFAULT_PASS: "guest"
    ports:
      - "${MY_DOCKER_IP:-127.0.0.1}:5672:5672"
      - "${MY_DOCKER_IP:-127.0.0.1}:15672:15672"
    volumes:
      - ~/.docker-conf/rabbitmq/data/:/var/lib/rabbitmq/
      - ~/.docker-conf/rabbitmq/log/:/var/log/rabbitmq

  consumer:
    build: ./consumer
    volumes:
      - ./:/builds/sitefotos-backend/sitefotos
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      DATABASE_PARAMS_HOST: ${DATABASE_PARAMS_HOST-db}
      DATABASE_PARAMS_PASSWORD: ${DATABASE_PARAMS_PASSWORD}
      DATABASE_PARAMS_USERNAME: ${DATABASE_PARAMS_USERNAME-root}
      DATABASE_PARAMS_DBNAME: ${DATABASE_PARAMS_DBNAME-sitefotos}
      DATABASE_PARAMS_PORT: ${DATABASE_PARAMS_PORT-3306}
      RABBITMQ_SERVICE_HOST: "rabbitmq"
      XDEBUG_CONFIG: "remote_enable=1 remote_host=host.docker.internal remote_port=9000 idekey=PHPSTORM remote_autostart=1"
      PHP_IDE_CONFIG: "serverName=docker"
      AWS_ACCESS_KEY: ${AWS_ACCESS_KEY}
      AWS_SECRET: ${AWS_SECRET}
    links:
      - db
      - rabbitmq
    depends_on:
      - db
      - rabbitmq

  bun-server:
    build:
      context: ./bun-server
      target: development
    restart: always
    ports:
      - "${MY_DOCKER_IP:-127.0.0.1}:3005:3005"
    volumes:
      - ./bun-server:/app
    extra_hosts:
      - "host.docker.internal:host-gateway"
